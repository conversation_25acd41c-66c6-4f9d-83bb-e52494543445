{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 15657897354478470176, "path": 14327853948049367427, "deps": [[784494742817713399, "tower_service", false, 13155949644745459096], [1906322745568073236, "pin_project_lite", false, 16884728351069996594], [4121350475192885151, "iri_string", false, 13461428814268737779], [5695049318159433696, "tower", false, 17325600401559988751], [7712452662827335977, "tower_layer", false, 9654882290405612424], [7896293946984509699, "bitflags", false, 5018574155598729807], [9010263965687315507, "http", false, 19658731293818321], [10629569228670356391, "futures_util", false, 7610032500833540501], [14084095096285906100, "http_body", false, 16100739262482986788], [16066129441945555748, "bytes", false, 14456324145966316533]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-591f8f08d3716213\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}