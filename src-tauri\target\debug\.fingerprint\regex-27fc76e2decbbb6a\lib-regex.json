{"rustc": 1842507548689473721, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2241668132362809309, "path": 13650486088368559120, "deps": [[555019317135488525, "regex_automata", false, 6702375932056734966], [2779309023524819297, "aho_corasick", false, 5614686659328970803], [9408802513701742484, "regex_syntax", false, 928313489936520898], [15932120279885307830, "memchr", false, 11823714542254866694]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-27fc76e2decbbb6a\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}