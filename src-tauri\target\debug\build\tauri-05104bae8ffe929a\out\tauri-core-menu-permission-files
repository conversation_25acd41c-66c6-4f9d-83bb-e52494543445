["\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\menu\\autogenerated\\default.toml"]