{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2241668132362809309, "path": 1576101622367071602, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 16504552386680545214], [3150220818285335163, "url", false, 12538487695336247294], [3191507132440681679, "serde_untagged", false, 16765884570110887758], [4899080583175475170, "semver", false, 13624529366928771029], [5578504951057029730, "serde_with", false, 15063364073238345137], [5986029879202738730, "log", false, 3971471616658610279], [6606131838865521726, "ctor", false, 3111066180042418224], [7170110829644101142, "json_patch", false, 17281500774135924377], [8319709847752024821, "uuid", false, 7040753467390339839], [9010263965687315507, "http", false, 9323006860634765112], [9451456094439810778, "regex", false, 1430532093854065308], [9556762810601084293, "brotli", false, 5812391114309194998], [9689903380558560274, "serde", false, 6455913412164549927], [10806645703491011684, "thiserror", false, 2548765959207783775], [11989259058781683633, "dunce", false, 2250542971913747141], [13625485746686963219, "anyhow", false, 13670858471858965574], [15367738274754116744, "serde_json", false, 9914683138587549262], [15609422047640926750, "toml", false, 16591220244481757712], [15622660310229662834, "walkdir", false, 7015256281871817685], [15932120279885307830, "memchr", false, 11823714542254866694], [17146114186171651583, "infer", false, 7286898208533959511], [17155886227862585100, "glob", false, 1844680892817435948], [17186037756130803222, "phf", false, 16350730283568599049]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-63cf2fb7f22c7179\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}