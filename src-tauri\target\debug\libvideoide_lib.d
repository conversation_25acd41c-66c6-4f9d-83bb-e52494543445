D:\dev\videoide\src-tauri\target\debug\libvideoide_lib.rlib: D:\dev\videoide\src-tauri\build.rs D:\dev\videoide\src-tauri\capabilities D:\dev\videoide\src-tauri\lib\ffmpeg\ffmpeg.exe D:\dev\videoide\src-tauri\lib\ffmpeg\ffprobe.exe D:\dev\videoide\src-tauri\lib\mpv\libmpv-2.dll D:\dev\videoide\src-tauri\src\action_chain\commands.rs D:\dev\videoide\src-tauri\src\action_chain\mod.rs D:\dev\videoide\src-tauri\src\app_init.rs D:\dev\videoide\src-tauri\src\audio\mod.rs D:\dev\videoide\src-tauri\src\color\mod.rs D:\dev\videoide\src-tauri\src\common\mod.rs D:\dev\videoide\src-tauri\src\config\mod.rs D:\dev\videoide\src-tauri\src\effect\mod.rs D:\dev\videoide\src-tauri\src\encode\mod.rs D:\dev\videoide\src-tauri\src\filter\mod.rs D:\dev\videoide\src-tauri\src\fonts\mod.rs D:\dev\videoide\src-tauri\src\image\mod.rs D:\dev\videoide\src-tauri\src\lib.rs D:\dev\videoide\src-tauri\src\license\commands.rs D:\dev\videoide\src-tauri\src\license\crypto.rs D:\dev\videoide\src-tauri\src\license\license_manager.rs D:\dev\videoide\src-tauri\src\license\mod.rs D:\dev\videoide\src-tauri\src\license\network.rs D:\dev\videoide\src-tauri\src\license\validation_commands.rs D:\dev\videoide\src-tauri\src\license\version_manager.rs D:\dev\videoide\src-tauri\src\player\commands.rs D:\dev\videoide\src-tauri\src\player\mod.rs D:\dev\videoide\src-tauri\src\player\mpv.rs D:\dev\videoide\src-tauri\src\player\window_manager.rs D:\dev\videoide\src-tauri\src\settings.rs D:\dev\videoide\src-tauri\src\subtitle\mod.rs D:\dev\videoide\src-tauri\src\sys\mod.rs D:\dev\videoide\src-tauri\src\transform\mod.rs D:\dev\videoide\src-tauri\src\trim\mod.rs D:\dev\videoide\src-tauri\src\videotoimage\mod.rs D:\dev\videoide\src-tauri\src\watermark\mod.rs D:\dev\videoide\src-tauri\target\debug\build\VideoIDE-c6970a0b4182e85f\out\5da590c4a87ee48c375fd1c8f79e4843cf511078d12157eedcb24e8b4605cd0b D:\dev\videoide\src-tauri\tauri.conf.json
