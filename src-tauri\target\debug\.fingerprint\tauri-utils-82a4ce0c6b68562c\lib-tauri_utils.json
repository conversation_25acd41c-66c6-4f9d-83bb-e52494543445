{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 1576101622367071602, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 13409524748993387463], [3150220818285335163, "url", false, 12101033925353838191], [3191507132440681679, "serde_untagged", false, 13426454859820161665], [4899080583175475170, "semver", false, 9193758614179110835], [5578504951057029730, "serde_with", false, 1064105332438032837], [5986029879202738730, "log", false, 5354761331429757099], [6606131838865521726, "ctor", false, 3111066180042418224], [7170110829644101142, "json_patch", false, 17377780230453370394], [8319709847752024821, "uuid", false, 15924535753212695164], [9010263965687315507, "http", false, 19658731293818321], [9451456094439810778, "regex", false, 11954208443263387047], [9556762810601084293, "brotli", false, 13700087193540667202], [9689903380558560274, "serde", false, 13917790453763015151], [10806645703491011684, "thiserror", false, 4794224307179080737], [11989259058781683633, "dunce", false, 5162970056373538091], [13625485746686963219, "anyhow", false, 14624950242237906499], [15367738274754116744, "serde_json", false, 11096967495204620297], [15609422047640926750, "toml", false, 5017433474264006912], [15622660310229662834, "walkdir", false, 6694464254078303639], [15932120279885307830, "memchr", false, 7806156119520509878], [17146114186171651583, "infer", false, 14999104691432777702], [17155886227862585100, "glob", false, 9107329985247477000], [17186037756130803222, "phf", false, 17211246142016640563]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-82a4ce0c6b68562c\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}