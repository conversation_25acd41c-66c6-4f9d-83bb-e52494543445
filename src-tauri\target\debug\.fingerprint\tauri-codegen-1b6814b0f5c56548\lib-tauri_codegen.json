{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 3831501143037367577, "deps": [[654232091421095663, "tauri_utils", false, 2295584652320036746], [3060637413840920116, "proc_macro2", false, 17423250190087945303], [3150220818285335163, "url", false, 5661166185672621682], [4899080583175475170, "semver", false, 17343531450092427673], [4974441333307933176, "syn", false, 2116388865398820018], [7170110829644101142, "json_patch", false, 16226402856146284555], [7392050791754369441, "ico", false, 7810190140410088755], [8319709847752024821, "uuid", false, 16842928919343689851], [9556762810601084293, "brotli", false, 2015517877292894721], [9689903380558560274, "serde", false, 17474093151178861523], [9857275760291862238, "sha2", false, 6262373726484567995], [10806645703491011684, "thiserror", false, 15694148885202828826], [12687914511023397207, "png", false, 2253117781512528126], [13077212702700853852, "base64", false, 17679524410597206751], [15367738274754116744, "serde_json", false, 12121239228005152762], [15622660310229662834, "walkdir", false, 5954171369194611797], [17990358020177143287, "quote", false, 11683726084642195291]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-1b6814b0f5c56548\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}