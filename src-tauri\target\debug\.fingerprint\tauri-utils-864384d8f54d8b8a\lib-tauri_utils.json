{"rustc": 1842507548689473721, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 1576101622367071602, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 9772265849128343513], [3060637413840920116, "proc_macro2", false, 17423250190087945303], [3150220818285335163, "url", false, 5661166185672621682], [3191507132440681679, "serde_untagged", false, 16248949839485520203], [4899080583175475170, "semver", false, 17343531450092427673], [5578504951057029730, "serde_with", false, 8950070952909285869], [5986029879202738730, "log", false, 15221515644513511198], [6606131838865521726, "ctor", false, 3111066180042418224], [6913375703034175521, "schemars", false, 9987694933512684282], [7170110829644101142, "json_patch", false, 16226402856146284555], [8319709847752024821, "uuid", false, 16842928919343689851], [9010263965687315507, "http", false, 15591522107022600800], [9451456094439810778, "regex", false, 5636990638689624091], [9556762810601084293, "brotli", false, 2015517877292894721], [9689903380558560274, "serde", false, 17474093151178861523], [10806645703491011684, "thiserror", false, 15694148885202828826], [11655476559277113544, "cargo_metadata", false, 16724867418935554251], [11989259058781683633, "dunce", false, 4523355074108005186], [13625485746686963219, "anyhow", false, 4071682536771644650], [14232843520438415263, "html5ever", false, 5426949781463743671], [15088007382495681292, "kuchiki", false, 9344718719304455908], [15367738274754116744, "serde_json", false, 12121239228005152762], [15609422047640926750, "toml", false, 9566884521906236232], [15622660310229662834, "walkdir", false, 5954171369194611797], [15932120279885307830, "memchr", false, 16610085938038584069], [17146114186171651583, "infer", false, 4128101931697914921], [17155886227862585100, "glob", false, 2202348345477578327], [17186037756130803222, "phf", false, 2075687763136918491], [17990358020177143287, "quote", false, 11683726084642195291]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-864384d8f54d8b8a\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}