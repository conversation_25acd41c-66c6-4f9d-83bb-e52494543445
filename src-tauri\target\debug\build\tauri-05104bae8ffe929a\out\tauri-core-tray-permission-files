["\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\tray\\autogenerated\\commands\\get_by_id.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\tray\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\tray\\autogenerated\\commands\\remove_by_id.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\tray\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\tray\\autogenerated\\commands\\set_icon_as_template.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\tray\\autogenerated\\commands\\set_menu.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\tray\\autogenerated\\commands\\set_show_menu_on_left_click.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\tray\\autogenerated\\commands\\set_temp_dir_path.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\tray\\autogenerated\\commands\\set_title.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\tray\\autogenerated\\commands\\set_tooltip.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\tray\\autogenerated\\commands\\set_visible.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\tray\\autogenerated\\default.toml"]