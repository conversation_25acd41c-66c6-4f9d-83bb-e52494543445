{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[18251603632334587267, "build_script_build", false, 6063971013977420680], [12092653563678505622, "build_script_build", false, 11488185819966329377], [1797035611096599003, "build_script_build", false, 16054125151401836068], [3834743577069889284, "build_script_build", false, 1662049311352946940], [422130612855741759, "build_script_build", false, 13925963221288133095], [17218623086136245857, "build_script_build", false, 12908770590362061471], [13496694572208715981, "build_script_build", false, 5811706155373916599]], "local": [{"RerunIfChanged": {"output": "debug\\build\\VideoIDE-09047f5bbc0b17c4\\output", "paths": ["tauri.conf.json", "capabilities", "lib\\ffmpeg\\ffmpeg.exe", "lib\\ffmpeg\\ffprobe.exe", "lib\\mpv\\libmpv-2.dll"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}