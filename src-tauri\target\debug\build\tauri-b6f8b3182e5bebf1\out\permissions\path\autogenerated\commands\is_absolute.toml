# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-is-absolute"
description = "Enables the is_absolute command without any pre-configured scope."
commands.allow = ["is_absolute"]

[[permission]]
identifier = "deny-is-absolute"
description = "Denies the is_absolute command without any pre-configured scope."
commands.deny = ["is_absolute"]
