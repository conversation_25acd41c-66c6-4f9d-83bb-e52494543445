{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 5599815348652095103, "deps": [[40386456601120721, "percent_encoding", false, 5774208227257979886], [654232091421095663, "tauri_utils", false, 18438473260592292545], [1200537532907108615, "url<PERSON><PERSON>n", false, 16504552386680545214], [1967864351173319501, "muda", false, 4297243655753037548], [2013030631243296465, "webview2_com", false, 17757907782243993341], [3150220818285335163, "url", false, 12538487695336247294], [3331586631144870129, "getrandom", false, 9537575876974270122], [4143744114649553716, "raw_window_handle", false, 761551120778097460], [4919829919303820331, "serialize_to_javascript", false, 13124773624128628881], [5986029879202738730, "log", false, 3971471616658610279], [9010263965687315507, "http", false, 9323006860634765112], [9538054652646069845, "tokio", false, 12011641416591561937], [9689903380558560274, "serde", false, 6455913412164549927], [10229185211513642314, "mime", false, 8262560586961866025], [10806645703491011684, "thiserror", false, 2548765959207783775], [11989259058781683633, "dunce", false, 2250542971913747141], [12092653563678505622, "build_script_build", false, 11488185819966329377], [12304025191202589669, "tauri_runtime_wry", false, 7667924046707226861], [12565293087094287914, "window_vibrancy", false, 9999926619085562967], [12943761728066819757, "tauri_runtime", false, 8178471820195784107], [12986574360607194341, "serde_repr", false, 17048422120640123096], [13077543566650298139, "heck", false, 16545598617844259130], [13405681745520956630, "tauri_macros", false, 1434074766234474955], [13625485746686963219, "anyhow", false, 13670858471858965574], [14585479307175734061, "windows", false, 8509674396166251682], [15367738274754116744, "serde_json", false, 9914683138587549262], [16928111194414003569, "dirs", false, 11526360539400032117], [17155886227862585100, "glob", false, 1844680892817435948]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-7692278c76c814fc\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}