{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[18251603632334587267, "build_script_build", false, 1544403673566062629], [12092653563678505622, "build_script_build", false, 17263928026407336176], [1797035611096599003, "build_script_build", false, 7234108859933694855], [3834743577069889284, "build_script_build", false, 9071702883922272849], [422130612855741759, "build_script_build", false, 12280338093306484632], [17218623086136245857, "build_script_build", false, 1076547933943494600], [13496694572208715981, "build_script_build", false, 17339054185524812731]], "local": [{"RerunIfChanged": {"output": "debug\\build\\VideoIDE-c6970a0b4182e85f\\output", "paths": ["tauri.conf.json", "capabilities", "lib\\ffmpeg\\ffmpeg.exe", "lib\\ffmpeg\\ffprobe.exe", "lib\\mpv\\libmpv-2.dll"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}