{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ring\", \"std\"]", "declared_features": "[\"alloc\", \"aws-lc-rs\", \"aws-lc-rs-fips\", \"default\", \"ring\", \"std\"]", "target": 5054897795206437336, "profile": 2241668132362809309, "path": 4862106509718381626, "deps": [[2883436298747778685, "pki_types", false, 2678755922855376556], [5491919304041016563, "ring", false, 18197667882848342333], [8995469080876806959, "untrusted", false, 9568360405012542532]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-webpki-48bf4214645c7b17\\dep-lib-webpki", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}