{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 2241668132362809309, "path": 14327853948049367427, "deps": [[784494742817713399, "tower_service", false, 17199493460738521781], [1906322745568073236, "pin_project_lite", false, 12763558466956792014], [4121350475192885151, "iri_string", false, 14183630372373751581], [5695049318159433696, "tower", false, 8452151730340386960], [7712452662827335977, "tower_layer", false, 13830321230740852922], [7896293946984509699, "bitflags", false, 6604173462406470572], [9010263965687315507, "http", false, 9323006860634765112], [10629569228670356391, "futures_util", false, 11783736339991792845], [14084095096285906100, "http_body", false, 17198480642775928061], [16066129441945555748, "bytes", false, 8751024589011300014]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-1f6ef7adaf4f9d5f\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}