# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-rgba"
description = "Enables the rgba command without any pre-configured scope."
commands.allow = ["rgba"]

[[permission]]
identifier = "deny-rgba"
description = "Denies the rgba command without any pre-configured scope."
commands.deny = ["rgba"]
