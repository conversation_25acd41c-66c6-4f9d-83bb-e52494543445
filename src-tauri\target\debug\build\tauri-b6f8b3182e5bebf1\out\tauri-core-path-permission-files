["\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-b6f8b3182e5bebf1\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-b6f8b3182e5bebf1\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-b6f8b3182e5bebf1\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-b6f8b3182e5bebf1\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-b6f8b3182e5bebf1\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-b6f8b3182e5bebf1\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-b6f8b3182e5bebf1\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-b6f8b3182e5bebf1\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-b6f8b3182e5bebf1\\out\\permissions\\path\\autogenerated\\default.toml"]