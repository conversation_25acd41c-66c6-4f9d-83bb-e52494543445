{"rustc": 1842507548689473721, "features": "[\"client\", \"default\", \"http1\", \"http2\"]", "declared_features": "[\"capi\", \"client\", \"default\", \"ffi\", \"full\", \"http1\", \"http2\", \"nightly\", \"server\", \"tracing\"]", "target": 9574292076208557625, "profile": 10563684691529833281, "path": 5654738806465879363, "deps": [[1569313478171189446, "want", false, 4998379855833301698], [1811549171721445101, "futures_channel", false, 8105674918046828167], [1906322745568073236, "pin_project_lite", false, 12763558466956792014], [3666196340704888985, "smallvec", false, 4251577208181119730], [6163892036024256188, "httparse", false, 298247702340952051], [7695812897323945497, "itoa", false, 7946828202873122978], [9010263965687315507, "http", false, 9323006860634765112], [9538054652646069845, "tokio", false, 12011641416591561937], [10629569228670356391, "futures_util", false, 11783736339991792845], [14084095096285906100, "http_body", false, 17198480642775928061], [14359893265615549706, "h2", false, 15549536962723533726], [16066129441945555748, "bytes", false, 8751024589011300014]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-da0fc099983c9118\\dep-lib-hyper", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}