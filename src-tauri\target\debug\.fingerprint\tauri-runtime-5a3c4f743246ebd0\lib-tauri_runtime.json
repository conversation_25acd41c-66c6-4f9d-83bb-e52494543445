{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2241668132362809309, "path": 1995976849762621589, "deps": [[654232091421095663, "tauri_utils", false, 2013371331527662952], [3150220818285335163, "url", false, 12538487695336247294], [4143744114649553716, "raw_window_handle", false, 761551120778097460], [7606335748176206944, "dpi", false, 4771223980154932384], [9010263965687315507, "http", false, 9323006860634765112], [9689903380558560274, "serde", false, 6455913412164549927], [10806645703491011684, "thiserror", false, 2548765959207783775], [12943761728066819757, "build_script_build", false, 17884493727853822448], [14585479307175734061, "windows", false, 8509674396166251682], [15367738274754116744, "serde_json", false, 9914683138587549262], [16727543399706004146, "cookie", false, 8122097352104842425]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-5a3c4f743246ebd0\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}