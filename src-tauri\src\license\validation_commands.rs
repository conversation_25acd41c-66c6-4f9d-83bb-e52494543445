// 版本验证相关的Tauri命令

use super::version_manager::{
    app_is_free, validate_action_count, validate_batch_processing, validate_folder_shortcut,
    validate_media_duration, FreeVersionLimits, ValidationResult,
};
use tauri::command;

/// 验证媒体文件时长限制
#[command]
pub async fn validate_media_duration_command(
    duration_seconds: f64,
) -> Result<ValidationResult, String> {
    Ok(validate_media_duration(duration_seconds))
}

/// 验证动作数量限制
#[command]
pub async fn validate_action_count_command(current_count: u32) -> Result<ValidationResult, String> {
    Ok(validate_action_count(current_count))
}

/// 验证批处理权限
#[command]
pub async fn validate_batch_processing_command() -> Result<ValidationResult, String> {
    Ok(validate_batch_processing())
}

/// 验证文件夹选择快捷键权限
#[command]
pub async fn validate_folder_shortcut_command() -> Result<ValidationResult, String> {
    Ok(validate_folder_shortcut())
}

/// 检查是否为免费版本
#[command]
pub async fn is_free_version_command() -> Result<bool, String> {
    Ok(app_is_free())
}

/// 获取免费版本限制信息
#[command]
pub async fn get_free_version_limits() -> Result<FreeVersionLimitsInfo, String> {
    Ok(FreeVersionLimitsInfo {
        max_media_duration_minutes: FreeVersionLimits::MAX_MEDIA_DURATION_MINUTES,
        max_actions_count: FreeVersionLimits::MAX_ACTIONS_COUNT,
        allow_batch_processing: FreeVersionLimits::ALLOW_BATCH_PROCESSING,
        allow_folder_shortcut: FreeVersionLimits::ALLOW_FOLDER_SHORTCUT,
    })
}

/// 免费版本限制信息结构体
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct FreeVersionLimitsInfo {
    pub max_media_duration_minutes: u32,
    pub max_actions_count: u32,
    pub allow_batch_processing: bool,
    pub allow_folder_shortcut: bool,
}

/// 综合版本验证
#[command]
pub async fn validate_version_comprehensive(
    duration_seconds: Option<f64>,
    action_count: Option<u32>,
    is_batch_processing: Option<bool>,
    is_using_folder_shortcut: Option<bool>,
) -> Result<ValidationResult, String> {
    // 如果不是免费版本，直接通过验证
    if !app_is_free() {
        return Ok(ValidationResult {
            is_valid: true,
            error_message: None,
            warning_message: None,
        });
    }

    // 验证媒体文件时长
    if let Some(duration) = duration_seconds {
        let result = validate_media_duration(duration);
        if !result.is_valid {
            return Ok(result);
        }
    }

    // 验证动作数量
    if let Some(count) = action_count {
        let result = validate_action_count(count);
        if !result.is_valid {
            return Ok(result);
        }
    }

    // 验证批处理权限
    if let Some(true) = is_batch_processing {
        let result = validate_batch_processing();
        if !result.is_valid {
            return Ok(result);
        }
    }

    // 验证文件夹快捷键权限
    if let Some(true) = is_using_folder_shortcut {
        let result = validate_folder_shortcut();
        if !result.is_valid {
            return Ok(result);
        }
    }

    Ok(ValidationResult {
        is_valid: true,
        error_message: None,
        warning_message: None,
    })
}
