{"rustc": 1842507548689473721, "features": "[\"log\", \"logging\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 5788819337146887687, "path": 11320133501219741205, "deps": [[2883436298747778685, "pki_types", false, 2678755922855376556], [3722963349756955755, "once_cell", false, 14119862828072357524], [5491919304041016563, "ring", false, 18197667882848342333], [5986029879202738730, "log", false, 3971471616658610279], [6528079939221783635, "zeroize", false, 7287423058953043120], [16400140949089969347, "build_script_build", false, 13843546460016762769], [17003143334332120809, "subtle", false, 4613014700335011774], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 10705457052890499803]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-f7db8a3111a94255\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}