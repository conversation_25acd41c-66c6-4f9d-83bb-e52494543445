# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-normalize"
description = "Enables the normalize command without any pre-configured scope."
commands.allow = ["normalize"]

[[permission]]
identifier = "deny-normalize"
description = "Denies the normalize command without any pre-configured scope."
commands.deny = ["normalize"]
