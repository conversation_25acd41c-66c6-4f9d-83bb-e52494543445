{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 10240568608642146247, "deps": [[654232091421095663, "tauri_utils", false, 2295584652320036746], [4899080583175475170, "semver", false, 17343531450092427673], [6913375703034175521, "schemars", false, 9987694933512684282], [7170110829644101142, "json_patch", false, 16226402856146284555], [9689903380558560274, "serde", false, 17474093151178861523], [12714016054753183456, "tauri_winres", false, 8211873917301898100], [13077543566650298139, "heck", false, 3837805312720259123], [13475171727366188400, "cargo_toml", false, 10244038831571897636], [13625485746686963219, "anyhow", false, 4071682536771644650], [15367738274754116744, "serde_json", false, 12121239228005152762], [15609422047640926750, "toml", false, 9566884521906236232], [15622660310229662834, "walkdir", false, 5954171369194611797], [16928111194414003569, "dirs", false, 18417576286179357654], [17155886227862585100, "glob", false, 2202348345477578327]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-250bbf7b61faea0a\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}