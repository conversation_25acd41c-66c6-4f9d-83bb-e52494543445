{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 5599815348652095103, "deps": [[40386456601120721, "percent_encoding", false, 866291264187665524], [654232091421095663, "tauri_utils", false, 6409919863348867524], [1200537532907108615, "url<PERSON><PERSON>n", false, 13409524748993387463], [1967864351173319501, "muda", false, 17588036490609404347], [2013030631243296465, "webview2_com", false, 8009150793477564317], [3150220818285335163, "url", false, 12101033925353838191], [3331586631144870129, "getrandom", false, 10664656861440682309], [4143744114649553716, "raw_window_handle", false, 8758997598796580990], [4919829919303820331, "serialize_to_javascript", false, 6941595937515564691], [5986029879202738730, "log", false, 5354761331429757099], [9010263965687315507, "http", false, 19658731293818321], [9538054652646069845, "tokio", false, 17387670018086847416], [9689903380558560274, "serde", false, 13917790453763015151], [10229185211513642314, "mime", false, 12208221640663408266], [10806645703491011684, "thiserror", false, 4794224307179080737], [11989259058781683633, "dunce", false, 5162970056373538091], [12092653563678505622, "build_script_build", false, 17263928026407336176], [12304025191202589669, "tauri_runtime_wry", false, 17055490234321897652], [12565293087094287914, "window_vibrancy", false, 13964694267294013813], [12943761728066819757, "tauri_runtime", false, 8158530261601759367], [12986574360607194341, "serde_repr", false, 17048422120640123096], [13077543566650298139, "heck", false, 8645442593182876745], [13405681745520956630, "tauri_macros", false, 7814935160113072451], [13625485746686963219, "anyhow", false, 14624950242237906499], [14585479307175734061, "windows", false, 6878485785883966654], [15367738274754116744, "serde_json", false, 11096967495204620297], [16928111194414003569, "dirs", false, 6669173218153952016], [17155886227862585100, "glob", false, 9107329985247477000]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-d012ae212aa79dec\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}