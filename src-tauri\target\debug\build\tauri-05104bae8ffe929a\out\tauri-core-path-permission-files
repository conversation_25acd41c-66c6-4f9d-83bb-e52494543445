["\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\dev\\videoide\\src-tauri\\target\\debug\\build\\tauri-05104bae8ffe929a\\out\\permissions\\path\\autogenerated\\default.toml"]