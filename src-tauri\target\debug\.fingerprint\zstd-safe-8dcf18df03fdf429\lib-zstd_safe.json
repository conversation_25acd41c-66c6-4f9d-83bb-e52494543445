{"rustc": 1842507548689473721, "features": "[\"std\"]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"seekable\", \"std\", \"thin\", \"thin-lto\", \"zdict_builder\", \"zstdmt\"]", "target": 13834647262792939399, "profile": 11535200890256339260, "path": 2426292180931543228, "deps": [[8373447648276846408, "zstd_sys", false, 7734137461488631350], [15788444815745660356, "build_script_build", false, 10205942456923873901]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zstd-safe-8dcf18df03fdf429\\dep-lib-zstd_safe", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}