cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=D:\dev\videoide\src-tauri\target\debug\build\tauri-plugin-drag-89f08f0769f695cd\out\tauri-plugin-drag-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-drag-2.1.0\src\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
