{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 266013487267336723, "deps": [[376837177317575824, "softbuffer", false, 780035089409845079], [654232091421095663, "tauri_utils", false, 2013371331527662952], [2013030631243296465, "webview2_com", false, 17757907782243993341], [3150220818285335163, "url", false, 12538487695336247294], [3722963349756955755, "once_cell", false, 14119862828072357524], [4143744114649553716, "raw_window_handle", false, 761551120778097460], [5986029879202738730, "log", false, 3971471616658610279], [8826339825490770380, "tao", false, 797404050889136848], [9010263965687315507, "http", false, 9323006860634765112], [9141053277961803901, "wry", false, 2844227519610358585], [12304025191202589669, "build_script_build", false, 9196162610147474893], [12943761728066819757, "tauri_runtime", false, 13544066230003497757], [14585479307175734061, "windows", false, 8509674396166251682]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-42d0f04a1ce7b2e7\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}