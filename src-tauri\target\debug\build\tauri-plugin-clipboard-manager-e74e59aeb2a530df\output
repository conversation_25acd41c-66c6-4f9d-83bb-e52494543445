cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=D:\dev\videoide\src-tauri\target\debug\build\tauri-plugin-clipboard-manager-e74e59aeb2a530df\out\tauri-plugin-clipboard-manager-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\D:\scoop\persist\rustup\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-clipboard-manager-2.3.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
